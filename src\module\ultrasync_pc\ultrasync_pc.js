import '../../common/console'
import App from './App'
import store from './store'
import router from './router'
import Vue from 'vue'
import i18n from '@/common/i18n'
import vuescroll from 'vuescroll';
import VueTouch from 'vue-touch'
import ElementUI from 'element-ui';
import VueVirtualScroller from 'vue-virtual-scroller'
import '../../../node_modules/vue-virtual-scroller/dist/vue-virtual-scroller.css'
import mrAvatar from './MRComponents/mrAvatar'
import './directive'
import '../../common/console'
import logReporter from './lib/logReporter';
import { pwa } from '../../../config/index'
import './lib/checkSameWindow'
import contentmenu from 'v-contextmenu'
import 'v-contextmenu/dist/index.css'
import 'plyr/dist/plyr.css';
import '@/icons/index'
if (pwa) {
    require('./registerServiceWorker')
}
window.logReporter = logReporter;
let url=location.search||location.hash
let search=url.split('?')[1]
window.urlHash={}
if (search) {

    let arr=search.split('&');
    for(let item of arr){
        let key=item.split('=')[0]
        let value=item.split('=')[1]
        window.urlHash[key]=value;
    }
}
Vue.use(VueVirtualScroller)
Vue.use(vuescroll);
Vue.use(VueTouch,{name:'v-touch'})
Vue.component(mrAvatar.name,mrAvatar)
Vue.use(contentmenu)
Vue.prototype.$vuescrollConfig = {
    bar: {
        background: '#a9bfbe',
        onlyShowBarOnScroll:false,
        size:'10px',
    },
    rail:{
        size:'12px'
    }
};

Vue.use(ElementUI);

// 安装权限管理器插件
import { install as PermissionPlugin } from '@/common/permission';
import permissionManager from '@/common/permission';
Vue.use(PermissionPlugin);

// 初始化区域权限管理器（应用启动时）
permissionManager.initializeRegionPermissions().then(() => {
    console.log('PC端区域权限管理器初始化完成');
}).catch(error => {
    console.error('PC端区域权限管理器初始化失败:', error);
});

const customRootObject ={ //退出登录后不会被重置
    eventBus:new Vue(),
    platformToast:null, //挂载框架的Toast方法给公共文件使用，让公共文件使用Toast与框架无关
}
const rootObject ={  //退出登录后被重置
    uploadList:[],//file文件上传队列
    transmitQueue:{},//云收藏，聊天界面转发图片消息队列
    deleteQueue:[],//删除列表
    endMessageIds:{},
    galleryCallback:null,
    controllerImgMap:{},//工作站转发文件暂存对象
    aiAnalyzeImgMap:{},//DROC等请求的AI分析暂存对象
    auto_upload_temp:{},//DR自动上传暂存对象
    droc_auto_upload_temp:{},//DROC自动上传报告暂存对象
    resourceReviewQueue:{},
    importExamImageTempQueue:[],//检查图像入库临时列表,
    transmitQueue_StandaloneWorkstation:{},//独立工作站待转发检查信息
    updateGroupAvatarUserList:{},//待更新群头像的用户列表
    loadingAndRenderHistory:{},//是否等待更新历史记录更新渲染
    isScrollingList:{},//是否正在滚动中[cid]
    withDrawList:{},//撤回列表
    withDrawTimeList:{},//撤回消息的时间
    currentLiveCid:0,//当前正在接受或者发起直播的群id
    transmitTempList:[],//转发临时存储文件
    conversation_event:{},//conversation的独立事件，不受重连影响
}
window.vm=new Vue({
    el: '#app',
    store,
    router,
    i18n,
    template: '<App/>',
    components: { App },
    data(){
        return {
            ...customRootObject,
            ...rootObject
        }
    }
})
window.vm.$root.resetCustomRootObject=function(){ //重置rootObject
    Object.keys(rootObject).forEach(key=>{
        window.vm.$root[key] = rootObject[key]
    })

}
