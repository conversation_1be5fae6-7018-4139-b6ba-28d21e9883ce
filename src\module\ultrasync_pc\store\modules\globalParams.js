import cloneDeep from 'lodash/cloneDeep'
import Vue from 'vue'
import permissionManager from '@/common/permission'
const initState ={
    isCef:false,
    isMaxScreen:false,
    auto_download:{
        enable:0
    },//自动下载设置
    auto_push_stream:{
        enable:0,
        record_mode:0
    },//自动推流设置
    istation_info:{
        Show:0
    },//istation设置
    realtime_ultrasound_mode:false,//进入istation
    isCE:false,
    region:'',
    functionsStatus:{
        live:1, //直播
        library:1, //图书馆
        cloudStatistic:1, //云端统计
        breastCases:1, //乳腺病例库
        breastAI:1, //小麦同学
        drAIAssistant:1, //dr助手
        groupset:1, //群落
        wechat:1, //微信功能
        obstetricalAI:1, //产科AI
        tvwall:1, //电视墙
        qcStatistics:1, //bi统计
        referralCode: 1,//推荐码
        webShareScreen:1,//web推桌面
        webTvwallEnterConversation:1,//web电视墙进入会话
        ai:1, //AI应用
        smartEdTechTraining:1, //智能教培
        ultrasoundQCReport:1, //超声质控报告
        club:1, //Mindray Club
        professionalIdentityForce:0,//职业身份强制设置

    },
    closedNotifyBar:false,//是否已经关闭通知栏
    announcementContent:'',//当前公告的内容
    init:false
}

export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'globalParams',cloneDeep(initState))
            }
        },
        updateGlobalParams(state, valObj) {
            for (let key in valObj) {
                Vue.set(state,key,valObj[key])
            }

            // 如果更新了region，同步更新RegionPermissionManager
            if (valObj.region !== undefined && permissionManager && permissionManager.regionManager) {
                try {
                    permissionManager.regionManager.updateRegionFunctions(
                        state.functionsStatus,
                        state.region
                    );
                    console.log('RegionPermissionManager已同步更新region');
                } catch (error) {
                    console.error('同步更新RegionPermissionManager的region失败:', error);
                }
            }
        },
        updateGlobalAutoDownload(state,valobj) {
            state.auto_download = valobj;
        },
        updateGlobalAutoPushStream(state,valobj) {
            state.auto_push_stream = valobj;
        },
        updateFunctionsStatus(state,valObj){
            for (let key in valObj) {
                Vue.set(state.functionsStatus,key,valObj[key])
            }

            // 同步更新RegionPermissionManager
            if (permissionManager && permissionManager.regionManager) {
                try {
                    permissionManager.regionManager.updateRegionFunctions(
                        state.functionsStatus,
                        state.region
                    );
                    console.log('RegionPermissionManager已同步更新functionsStatus');
                } catch (error) {
                    console.error('同步更新RegionPermissionManager失败:', error);
                }
            }
        },
        updateGlobalIstationInfo(state,valobj) {
            state.istation_info = valobj;
        },
        clearGlobalParams(state){
            state={
                isCef:false,
                isMaxScreen:false,
                auto_download:{
                    enable:0
                },//自动下载设置
                auto_push_stream:{
                    enable:0,
                    record_mode:0
                },//自动推流设置
                istation_info:{
                    Show:0
                },//istation设置
                realtime_ultrasound_mode:false,//进入istation
                isCE:false,
            }
        },
    },
    actions: {},
    getters: {}
}
