<template>
    <div class="header-bar">
        <div class="header-bar-left" :style="{ '--header-left-bg': `url(${headerLeftBg})` }" @click="handleLogoClick" :title="user.role | FilterRole">
            <img class="header-bar-logo" :src="mindrayLogo" alt="">
        </div>
        <div class="header-bar-right" :style="{ '--header-right-bg': `url(${headerRightBg})` }">
            <div class="hospital"><img class="hospital-logo" src="" alt=""><span>迈瑞云PACS测试医院</span></div>
            <div class="header-bar-right-divider"></div>

            <!-- 用户下拉菜单 -->
            <div class="user-dropdown">
                <el-dropdown trigger="click" placement="bottom-end" @command="handleCommand" ref="userDropdown">
                    <span class="user-info">
                        <span class="username">{{ user.nickname || '用户' }}</span>
                        <i class="el-icon-arrow-down el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="cloudFavorites">
                            <i class="el-icon-star-on"></i>
                            {{ $t('cloud_favorites') }}
                        </el-dropdown-item>
                        <el-dropdown-item command="accountSecurity">
                            <i class="el-icon-user"></i>
                            {{ $t('personal_setting_title') }}
                        </el-dropdown-item>
                        <el-dropdown-item command="inviteRegistration">
                            <i class="el-icon-message"></i>
                            {{ $t('invite_registration') }}
                        </el-dropdown-item>
                        <!-- 多语言二级菜单 -->
                        <li class="custom-dropdown-item">
                            <el-dropdown
                                trigger="hover"
                                placement="right-start"
                                @command="handleLanguageCommand"
                                @visible-change="handleLanguageDropdownVisible"
                                ref="languageDropdown"
                            >
                                <span class="language-menu-item" @click.prevent.stop @mousedown.prevent.stop>
                                    <i class="el-icon-s-tools"></i>
                                    {{ $t('international_title') }} - {{ currentLanguageLabel }}
                                    <i class="el-icon-arrow-right"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown" class="language-submenu">
                                    <el-dropdown-item
                                        v-for="lang in languageOptions"
                                        :key="lang.value"
                                        :command="lang.value"
                                        :class="{ 'is-active': curLanguage === lang.value }"
                                    >
                                        {{ lang.label }}
                                        <i v-if="curLanguage === lang.value" class="el-icon-check"></i>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </li>
                        <el-dropdown-item divided command="logout">
                            <i class="el-icon-switch-button"></i>
                            {{ $t('logout') }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>

            <!-- 窗口控制按钮 -->
            <div  class="window-controls" @mousedown.stop v-if="isCef">
                <i class="control-btn minimize-btn el-icon-minus" @click="minApp" title="最小化"></i>
                <i class="control-btn maximize-btn el-icon-full-screen" @click="maximizeApp" title="最大化"></i>
                <i class="control-btn close-btn el-icon-close" @click="closeApp" title="关闭"></i>
            </div>
        </div>

                <!-- 个人设置弹窗 - 动态加载 -->
        <component
            :is="personalSettingComponent"
            v-model="personalSettingVisible"
            @input="handlePersonalSettingVisibleChange"
        />

        <!-- 我的云收藏弹窗 - 动态加载 -->
        <component
            :is="favoritesComponent"
            v-model="favoritesVisible"
            @input="handleFavoritesVisibleChange"
        />

        <!-- 邀请注册弹窗 - 动态加载 -->
        <component
            :is="inviteRegistrationComponent"
            v-model="inviteRegistrationVisible"
            @input="handleInviteRegistrationVisibleChange"
        />
    </div>
</template>
<script>
import { headerLeftBg, headerRightBg, mindrayLogo } from "@/icons/base64Image.js";
import base from "../lib/base";
import appOperateTool from "../lib/appOperateTool";
import service from "../service/service";
import Tool from "@/common/tool.js";
import { setLanguage, getLanguage } from "@/common/i18n";
export default {
    name: 'headerBar',
    components: {},
    mixins: [base, appOperateTool],
    filters: {
        FilterRole(val) {
            let arrayData = {
                0: "临时用户",
                1: "普通用户",
                2: "管理员",
                3: "超级管理员",
                4: "主任",
            };
            return arrayData[val];
        },
    },
    data() {
        return {
            headerLeftBg,
            headerRightBg,
            mindrayLogo,
            personalSettingVisible: false, // 个人设置弹窗是否显示
            personalSettingComponent: null, // 个人设置组件缓存
            favoritesVisible: false, // 我的云收藏弹窗是否显示
            favoritesComponent: null, // 我的云收藏组件缓存
            inviteRegistrationVisible: false, // 邀请注册弹窗是否显示
            inviteRegistrationComponent: null, // 邀请注册组件缓存
            // 语言选项（参考 systemSetting.vue）
            languageOptions: [
                {
                    value: "CN",
                    label: "简体中文",
                },
                {
                    value: "EN",
                    label: "English",
                },
                {
                    value: "ES",
                    label: "Español",
                },
                {
                    value: "PTBR",
                    label: "Português",
                },
                {
                    value: "RU",
                    label: "Русский язык",
                },
                {
                    value: "DE",
                    label: "Deutsch",
                },
                {
                    value: "FR",
                    label: "Français",
                },
                {
                    value: "IT",
                    label: "Italiano",
                },
            ]
        }
    },
    computed: {
        curLanguage() {
            return getLanguage();
        },
        currentLanguageLabel() {
            const currentLang = this.languageOptions.find(lang => lang.value === this.curLanguage);
            return currentLang ? currentLang.label : '';
        }
    },
    methods: {
        handleLogoClick(){
            console.log(window.vm.$store.state)
        },
        handleCommand(command) {
            switch (command) {
            case 'cloudFavorites':
                this.handleCloudFavorites();
                break;
            case 'accountSecurity':
                this.handleAccountSecurity();
                break;
            case 'inviteRegistration':
                this.handleInviteRegistration();
                break;
            case 'hiddenInfo':
                this.handleHiddenInfo();
                break;
            case 'about':
                this.handleAbout();
                break;
            case 'logout':
                this.handleLogout();
                break;
            default:
                break;
            }
        },

        // 预留的功能函数
        async handleAccountSecurity() {
            // 动态加载并打开个人设置弹窗
            await this.loadPersonalSetting();
            this.$nextTick(() => {
                this.personalSettingVisible = true;
            });
        },

        // 动态加载PersonalSetting组件
        async loadPersonalSetting() {
            if (!this.personalSettingComponent) {
                try {
                    // 在用户确定打开时才动态导入组件
                    const module = await import("../pages/personalSetting.vue");
                    this.personalSettingComponent = module.default;
                } catch (error) {
                    console.error('加载PersonalSetting组件失败:', error);
                    this.$message.error('加载个人设置失败，请重试');
                }
            }
        },

        // 处理弹窗显示状态变化
        handlePersonalSettingVisibleChange(visible) {
            if (!visible) {
                // 弹窗关闭，延迟销毁组件以等待动画完成
                setTimeout(() => {
                    this.personalSettingComponent = null;
                }, 300); // 等待300ms动画完成
            }
        },

        // 我的云收藏
        async handleCloudFavorites() {
            // 动态加载并打开我的云收藏弹窗
            await this.loadFavorites();
            this.$nextTick(() => {
                this.favoritesVisible = true;
            });
        },

        // 动态加载Favorites组件
        async loadFavorites() {
            if (!this.favoritesComponent) {
                try {
                    const module = await import("../pages/favorites.vue");
                    this.favoritesComponent = module.default;
                } catch (error) {
                    console.error('加载Favorites组件失败:', error);
                    this.$message.error('加载我的云收藏失败，请重试');
                }
            }
        },

        // 处理我的云收藏弹窗显示状态变化
        handleFavoritesVisibleChange(visible) {
            if (!visible) {
                setTimeout(() => {
                    this.favoritesComponent = null;
                }, 300);
            }
        },

        // 邀请注册
        async handleInviteRegistration() {
            // 动态加载并打开邀请注册弹窗
            await this.loadInviteRegistration();
            this.$nextTick(() => {
                this.inviteRegistrationVisible = true;
            });
        },

        // 动态加载InviteRegistration组件
        async loadInviteRegistration() {
            if (!this.inviteRegistrationComponent) {
                try {
                    const module = await import("../pages/inviteRegistration.vue");
                    this.inviteRegistrationComponent = module.default;
                } catch (error) {
                    console.error('加载InviteRegistration组件失败:', error);
                    this.$message.error('加载邀请注册失败，请重试');
                }
            }
        },

        // 处理邀请注册弹窗显示状态变化
        handleInviteRegistrationVisibleChange(visible) {
            if (!visible) {
                setTimeout(() => {
                    this.inviteRegistrationComponent = null;
                }, 300);
            }
        },

        // 处理语言切换命令
        handleLanguageCommand(languageCode) {
            this.changeLang(languageCode);
        },

        // 处理语言下拉菜单显示状态变化
        handleLanguageDropdownVisible(visible) {
            // 当二级菜单显示时，可以在这里添加额外的逻辑
            // 目前主要是为了配合 :hide-on-click="false" 属性
        },

        // 切换语言方法（参考 systemSetting.vue）
        changeLang(lang) {
            if (lang === this.curLanguage) {
                return; // 如果是当前语言，不需要切换
            }
            if (this.isCef) {
                const params = {
                    language: lang,
                };
                Tool.createCWorkstationCommunicationMng({
                    name: "RequestSetClientConfInfo",
                    emitName: "NotifyRequestSetClientConfInfo",
                    params,
                    timeout: 5000,
                }).then((res) => {
                    if (res.error) {
                        console.error("RequestSetClientConfInfo error");
                        return;
                    }
                    this.$confirm(this.$t("effect_after_restart"), this.$t("tip_title"), {
                        confirmButtonText: this.$t("confirm_button_text"),
                        cancelButtonText: this.$t("cancel_button_text"),
                        type: "warning",
                    }).then(() => {
                        window.CWorkstationCommunicationMng.switchLanguage(lang);
                        this.$message.success(this.$t("change_language_success"));
                    }).catch(() => {});
                });
            } else {
                setLanguage(lang);
                this.$message.success(this.$t("change_language_success"));
            }
        },

        handleHiddenInfo() {
            // TODO: 实现隐藏病人信息功能
            console.log('隐藏病人信息');
        },

        handleAbout() {
            // TODO: 实现关于页面功能
            console.log('关于瑞影-云PACS');
        },

        handleLogout() {
            this.logout();
        },

        logout() {
            this.$root.eventBus.$emit("unBindControllerEvent");
            setTimeout(() => {
                if (window.main_screen && window.main_screen.gateway) {
                    window.main_screen.CloseSocket();
                }
                service
                    .logout({
                        user_id: this.user.uid,
                        client_uuid: this.user.client_uuid,
                    })
                    .then((res) => { })
                    .catch((res) => { });
                this.resetApp();
            }, 0);
        },

        resetApp() {
            window.CWorkstationCommunicationMng.CloseNewWindow();
            window.CWorkstationCommunicationMng.resetApp();
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            window.localStorage.setItem("account", "");
            window.localStorage.setItem("password", "");
            window.localStorage.setItem("loginToken", "");
            window.localStorage.removeItem("local_store_device_token");
            this.$store.commit("user/updateUser", {
                new_token: "",
            });
            window.CWorkstationCommunicationMng.ClearStartupOption();
            if (window.main_screen.CMonitorWallPush && window.main_screen.CMonitorWallPush.joined) {
                window.main_screen.CMonitorWallPush.LeaveChannelSilence();
            }

            // setTimeout(()=>{
            //     window.location.reload();
            // },300)
            this.$root.eventBus.$emit("reloadRouter");
        }
    }
}
</script>
<style scoped lang="scss">
.header-bar{
    height: 64px;
    user-select: none;
    background-image: linear-gradient(179deg, #fcfeff 0%, #dde3f0 100%);
    border-bottom: none;
    padding: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    color: rgba(0, 0, 0, 0.88);
    flex: 0 0 auto;
    .header-bar-left{
        width: 235px;
        min-width: 235px;
        background-color: linear-gradient(179deg, #fcfeff 0%, #dde3f0 100%);
        background-image: var(--header-left-bg);
        background-repeat: no-repeat;
        background-position-x: -20px;
        background-position-y: -8px;
        height: 100%;
        padding-left: 16px;
        margin-right: 195px;
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: bold;
        line-height: 55px;
        .header-bar-logo{
            width: 80px;
            height: 20px;
            margin-right: 16px;
        }
    }
    .header-bar-right{
        height: 100%;
        margin-left: auto;
        background-image: linear-gradient(180deg, #c4e2ff 0%, #dde3f0 100%);
        padding-right: 16px;
        position: relative;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        line-height: 55px;
        &:before{
            content: "";
            display: block;
            position: absolute;
            width: 80px;
            height: 100%;
            top: 0;
            left: -80px;
            background: var(--header-right-bg) no-repeat top right;
        }
        .hospital{
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-right: 30px;
            font-weight: bold;
            font-size: 16px;
            .hospital-logo{
                height: 30px;
                width: 30px;
                margin-right: 10px;
            }
        }
        .header-bar-right-divider{
            margin: 0 20px;
            height: 1.3em;
            position: relative;
            display: inline-block;
            vertical-align: middle;
            border-top: 0;
            border-inline-start: 1px solid rgba(5, 5, 5, 0.06);
            line-height: 1.5714285714285714;
            list-style: none;
            padding: 0;
            color: rgba(0, 0, 0, 0.88);
        }

        .user-dropdown {
            display: flex;
            align-items: center;

            .user-info {
                display: flex;
                align-items: center;
                cursor: pointer;
                padding: 4px 12px;
                border-radius: 4px;
                transition: background-color 0.3s;
                color: rgba(0, 0, 0, 0.88);
                font-size: 16px;
                box-sizing: border-box;
                height: 100%;
                overflow: hidden;
                &:hover {
                    background-color: rgba(0, 0, 0, 0.04);
                }

                .username {
                    margin-right: 4px;
                    font-weight: 500;
                }

                .el-icon-arrow-down {
                    font-size: 12px;
                    transition: transform 0.3s;
                }
            }
        }

        .window-controls {
            display: flex;
            align-items: center;
            margin-left: 12px;
            height: 100%;

            .control-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 30px;
                font-size: 16px;
                color: #909399;
                cursor: pointer;
                transition: all 0.15s ease;
                user-select: none;
                border-radius: 2px;
                margin-left: 1px;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.06);
                    color: #606266;
                }

                &.minimize-btn {
                    &:hover {
                        background-color: rgba(0, 0, 0, 0.08);
                        color: #303133;
                    }
                }

                &.maximize-btn {
                    &:hover {
                        background-color: rgba(0, 0, 0, 0.08);
                        color: #303133;
                    }
                }

                &.close-btn {
                    &:hover {
                        background-color: #f56c6c;
                        color: #fff;
                    }

                    &:active {
                        background-color: #f04747;
                    }
                }

                &:active {
                    background-color: rgba(0, 0, 0, 0.12);
                }
            }
        }
    }
}
</style>

<style lang="scss">
// 全局样式，用于下拉菜单
.el-dropdown-menu {
    min-width: 160px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-dropdown-menu__item {
        display: flex !important;
        align-items: center !important;
        padding: 10px 16px !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #606266 !important;
        cursor: pointer !important;
        outline: none !important;
        white-space: nowrap !important;

        i {
            margin-right: 8px !important;
            font-size: 14px !important;
            width: 16px !important;
            text-align: center !important;
            color: #909399 !important;
        }

        &:hover {
            background-color: #f5f7fa !important;
            color: #409eff !important;

            i {
                color: #409eff !important;
            }
        }

        &.is-divided {
            border-top: 1px solid #ebeef5 !important;
            margin-top: 6px !important;
        }

        &:focus {
            background-color: #f5f7fa !important;
            color: #409eff !important;
        }
    }
    .el-dropdown-menu__item--divided:before {
        margin: 0 !important;
    }

    // 自定义下拉菜单项样式，与 el-dropdown-menu__item 保持一致
    .custom-dropdown-item {
        display: flex !important;
        align-items: center !important;

        font-size: 14px !important;
        line-height: 1.5 !important;
        color: #606266 !important;
        cursor: pointer !important;
        outline: none !important;
        white-space: nowrap !important;
        list-style: none !important;
        margin: 0 !important;
        .el-dropdown{

        }
        .language-menu-item{
            padding: 10px 16px !important;
        }
        i {
            margin-right: 8px !important;
            font-size: 14px !important;
            width: 16px !important;
            text-align: center !important;
            color: #909399 !important;
        }

        &:hover {
            background-color: #f5f7fa !important;
            color: #409eff !important;

            i {
                color: #409eff !important;
            }
        }

        &:focus {
            background-color: #f5f7fa !important;
            color: #409eff !important;
        }
    }
}

/* 多语言菜单样式 */
.language-menu-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.language-menu-item .el-icon-arrow-right {
    margin-left: 8px;
    font-size: 12px;
    color: #909399;
}

.language-submenu {
    transform: translateX(8px) !important;
}

.language-submenu .el-dropdown-menu__item.is-active {
    background-color: #ecf5ff;
    color: #409eff;
}

.language-submenu .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.language-submenu .el-icon-check {
    color: #409eff;
    font-size: 14px;
}
</style>
