<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由匹配测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>路由权限匹配测试</h1>
        
        <div class="test-section">
            <h2>路径匹配测试</h2>
            <button onclick="testPathMatching()">测试路径匹配</button>
            <div id="pathMatchingResults"></div>
        </div>
    </div>

    <script>
        // 复制路径匹配逻辑
        function matchRoutePathStatic(actualPath, configPath) {
            // 如果配置路径不包含参数，使用简单的前缀匹配
            if (!configPath.includes(':')) {
                return actualPath.startsWith(configPath);
            }

            // 将路径分割为段
            const actualSegments = actualPath.split('/').filter(Boolean);
            const configSegments = configPath.split('/').filter(Boolean);

            // 如果段数不同，不匹配
            if (actualSegments.length !== configSegments.length) {
                return false;
            }

            // 逐段比较
            for (let i = 0; i < configSegments.length; i++) {
                const configSegment = configSegments[i];
                const actualSegment = actualSegments[i];

                // 如果是参数段（以:开头），跳过比较
                if (configSegment.startsWith(':')) {
                    continue;
                }

                // 如果不是参数段，必须完全匹配
                if (configSegment !== actualSegment) {
                    return false;
                }
            }

            return true;
        }

        function testPathMatching() {
            const resultsDiv = document.getElementById('pathMatchingResults');
            resultsDiv.innerHTML = '';

            const testCases = [
                {
                    name: 'add_attendee路由测试',
                    actualPath: '/main/index/chat_window/123/add_attendee',
                    configPath: '/main/index/chat_window/:cid/add_attendee',
                    expected: true
                },
                {
                    name: 'add_attendee路由测试 - 不同cid',
                    actualPath: '/main/index/chat_window/456/add_attendee',
                    configPath: '/main/index/chat_window/:cid/add_attendee',
                    expected: true
                },
                {
                    name: '不匹配的路由',
                    actualPath: '/main/index/chat_window/123/delete_attendee',
                    configPath: '/main/index/chat_window/:cid/add_attendee',
                    expected: false
                },
                {
                    name: '简单路径匹配',
                    actualPath: '/main/background_manage',
                    configPath: '/main/background_manage',
                    expected: true
                },
                {
                    name: '前缀匹配',
                    actualPath: '/main/background_manage/users',
                    configPath: '/main/background_manage',
                    expected: true
                }
            ];

            testCases.forEach(testCase => {
                const result = matchRoutePathStatic(testCase.actualPath, testCase.configPath);
                const success = result === testCase.expected;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${success ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>${testCase.name}</strong><br>
                    实际路径: ${testCase.actualPath}<br>
                    配置路径: ${testCase.configPath}<br>
                    期望结果: ${testCase.expected}<br>
                    实际结果: ${result}<br>
                    状态: ${success ? '✅ 通过' : '❌ 失败'}
                `;
                resultsDiv.appendChild(resultDiv);
            });
        }
    </script>
</body>
</html>
