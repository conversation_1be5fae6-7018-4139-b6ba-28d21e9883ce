# 公共权限管理系统

## 概述

这是一个统一的权限管理系统，从PC端项目(`src/module/ultrasync_pc/lib/permission`)移植到公共位置(`src/common/permission`)，以便移动端和PC端项目共享使用。

## 文件结构

```
src/common/permission/
├── constant.js                    # 权限相关常量定义
├── BasePermissionManager.js       # 权限管理器基类
├── PermissionManager.js          # 主权限管理器
├── RoutePermissionManager.js     # 路由权限管理器
├── ComponentPermissionManager.js # 组件权限管理器
├── FeaturePermissionManager.js   # 功能权限管理器
├── RegionPermissionManager.js    # 区域权限管理器
├── ConversationPermissionManager.js # 会话权限管理器
├── index.js                      # 入口文件，包含Vue插件
└── README.md                     # 说明文档
```

## 主要功能

### 1. 用户角色常量 (constant.js)
- `USER_ROLE`: 用户角色定义
- `PERMISSION_TYPE`: 权限类型常量
- `PERMISSION_ACTION`: 权限操作常量
- `PERMISSION_STATUS`: 权限状态常量

### 2. 权限管理器类型

#### BasePermissionManager
- 所有权限管理器的基类
- 提供通用的权限检查接口和缓存机制

#### PermissionManager
- 主权限管理器，统一管理所有子权限管理器
- 提供统一的权限检查接口
- 单例模式

#### RoutePermissionManager
- 路由级别的权限控制
- 支持白名单路由
- 支持动态路由权限配置
- 自动从路由配置中提取权限信息
- 支持OR/AND权限检查策略
- 已集成到PC端路由守卫中
- 提供全局单例模式

#### ComponentPermissionManager
- 组件级别的权限控制
- 支持组件显示/隐藏控制
- 支持元素级权限控制

#### FeaturePermissionManager
- 功能级别的权限控制
- API权限控制
- 数据权限控制

#### RegionPermissionManager
- 基于区域配置的功能权限
- 与Vuex store集成
- 支持动态配置更新

#### ConversationPermissionManager
- 会话内的角色权限控制
- 支持群主、管理员、普通成员等角色
- 消息、成员管理等权限控制

## 使用方法

### 1. 在Vue项目中安装插件

```javascript
import Vue from 'vue';
import { install } from '@/common/permission';

// 安装权限插件
Vue.use(install, {
    autoInit: false, // 是否自动初始化
    components: [] // 需要权限混入的组件名称列表
});
```

### 2. 初始化权限管理器

```javascript
import permissionManager from '@/common/permission';

// 初始化区域权限（应用启动时）
await permissionManager.initializeRegionPermissions(config);

// 初始化用户权限（用户登录后）
await permissionManager.initialize(userInfo, config);
```

### 3. 权限检查

```javascript
// 检查功能权限
const hasPermission = permissionManager.checkFeaturePermission('backgroundManage');

// 检查路由权限
const canAccess = permissionManager.checkRoutePermission('/admin');

// 检查组件权限
const isVisible = permissionManager.checkComponentPermission('button', 'delete');

// 检查区域权限
const isEnabled = permissionManager.checkRegionPermission('tvwall');

// 通用权限检查
const hasAccess = permissionManager.checkPermission({
    regionPermissionKey: 'live',
    featurePermissionKey: 'liveStart'
});
```

### 4. 在Vue组件中使用

```javascript
// 在组件中使用
export default {
    computed: {
        canDelete() {
            return this.$checkComponent('button', 'delete');
        },
        isAdmin() {
            return this.$isAdmin();
        }
    }
}
```

### 5. 使用v-permission指令

```html
<!-- 功能权限 -->
<button v-permission="'backgroundManage'">后台管理</button>

<!-- 组件权限 -->
<button v-permission.component="'delete'">删除</button>

<!-- 区域权限 -->
<div v-permission.region="'tvwall'">电视墙功能</div>

<!-- 复合权限 -->
<button v-permission="{regionPermissionKey: 'live', featurePermissionKey: 'liveStart'}">
    开始直播
</button>

<!-- 隐藏模式 -->
<div v-permission.hide="'admin'">管理员内容</div>

<!-- 禁用模式 -->
<button v-permission.disable="'delete'">删除按钮</button>
```

## 迁移说明

### PC端项目迁移
PC端项目的权限系统已经完全迁移到公共位置：

1. `src/module/ultrasync_pc/lib/permission/index.js` 现在直接从公共模块导入
2. `src/module/ultrasync_pc/lib/constants.js` 中的 `USER_ROLE` 从公共模块导入
3. 原有的权限管理器文件已删除，避免重复

### 移动端项目集成
移动端项目可以直接使用公共权限模块：

```javascript
import permissionManager from '@/common/permission';
```

## 注意事项

1. **向后兼容**: PC端项目的现有代码无需修改，所有API保持不变
2. **单例模式**: PermissionManager使用单例模式，确保全局唯一实例
3. **事件系统**: 权限变化会触发全局事件，组件可以监听权限变化
4. **缓存机制**: 权限检查结果会被缓存，提高性能
5. **错误处理**: 未初始化时权限检查会返回false并输出警告

## 扩展

如果需要添加新的权限类型或管理器，可以：

1. 继承 `BasePermissionManager` 创建新的权限管理器
2. 在 `PermissionManager` 中集成新的管理器
3. 在 `index.js` 中导出新的接口

## RoutePermissionManager 详细说明

### 功能特性

1. **路由配置集成**
   - 由各自的路由文件主动设置权限管理器配置
   - 支持从路由的meta字段中读取权限配置
   - PC端和移动端分别在各自的路由文件中初始化

2. **权限检查策略**
   - **OR策略**: 角色权限或特定权限任一满足即可（默认）
   - **AND策略**: 角色权限和特定权限都必须满足

3. **白名单机制**
   - 支持白名单路由，无需权限检查即可访问
   - 白名单由路由配置中的导出决定，默认为空

### 路由权限配置示例

```javascript
// 在路由配置中添加权限信息
{
    path: 'background_manage',
    name: 'background_manage',
    component: BackgroundManage,
    meta: {
        roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN],
        permissions: ['admin'],
        strategy: 'OR' // 或 'AND'
    }
}
```

### 使用方法

#### 在路由文件中初始化

```javascript
// 在路由文件中 (如 src/module/ultrasync_pc/router/index.js)
import { RoutePermissionManager } from '@/common/permission/index.js';

// 创建路由实例后
const router = new VueRouter({ routes });
const whiteList = ['/login', '/webLive', '/init'];

// 初始化权限管理器
const routePermissionManager = RoutePermissionManager.getGlobalInstance();
routePermissionManager.setRouterConfig(router, whiteList);
```

#### 在应用中使用

```javascript
import RoutePermissionManager from '@/common/permission/RoutePermissionManager.js';

// 获取全局实例
const routeManager = RoutePermissionManager.getGlobalInstance();

// 检查路由权限
const hasPermission = routeManager.hasPermission('/main/background_manage');

// 静态方法检查
const canAccess = RoutePermissionManager.checkRouteAccess('/main/background_manage');
```

### 集成状态

- ✅ **PC端**: 已集成到路由守卫中，自动进行权限检查
- ⚠️ **移动端**: 支持权限配置提取，但未添加路由守卫

### 测试

提供了测试页面用于验证功能：`src/common/permission/test-route-permission.html`
