{"version": 3, "file": "static/js/chunk-vue.ed93b810.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAiCA", "sources": ["webpack://ultrasync/./node_modules/element-ui/lib/utils/popper.js"], "sourcesContent": ["'use strict';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\n/**\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version {{version}}\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n//\n// Cross module loader\n// Supported: Node, AMD, Browser globals\n//\n;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory);\n    } else if ((typeof module === 'undefined' ? 'undefined' : _typeof(module)) === 'object' && module.exports) {\n        // Node. Does not work with strict CommonJS, but\n        // only CommonJS-like environments that support module.exports,\n        // like Node.\n        module.exports = factory();\n    } else {\n        // Browser globals (root is window)\n        root.Popper = factory();\n    }\n})(undefined, function () {\n\n    'use strict';\n\n    var root = window;\n\n    // default options\n    var DEFAULTS = {\n        // placement of the popper\n        placement: 'bottom',\n\n        gpuAcceleration: true,\n\n        // shift popper from its origin by the given amount of pixels (can be negative)\n        offset: 0,\n\n        // the element which will act as boundary of the popper\n        boundariesElement: 'viewport',\n\n        // amount of pixel used to define a minimum distance between the boundaries and the popper\n        boundariesPadding: 5,\n\n        // popper will try to prevent overflow following this order,\n        // by default, then, it could overflow on the left and on top of the boundariesElement\n        preventOverflowOrder: ['left', 'right', 'top', 'bottom'],\n\n        // the behavior used by flip to change the placement of the popper\n        flipBehavior: 'flip',\n\n        arrowElement: '[x-arrow]',\n\n        arrowOffset: 0,\n\n        // list of functions used to modify the offsets before they are applied to the popper\n        modifiers: ['shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle'],\n\n        modifiersIgnored: [],\n\n        forceAbsolute: false\n    };\n\n    /**\n     * Create a new Popper.js instance\n     * @constructor Popper\n     * @param {HTMLElement} reference - The reference element used to position the popper\n     * @param {HTMLElement|Object} popper\n     *      The HTML element used as popper, or a configuration used to generate the popper.\n     * @param {String} [popper.tagName='div'] The tag name of the generated popper.\n     * @param {Array} [popper.classNames=['popper']] Array of classes to apply to the generated popper.\n     * @param {Array} [popper.attributes] Array of attributes to apply, specify `attr:value` to assign a value to it.\n     * @param {HTMLElement|String} [popper.parent=window.document.body] The parent element, given as HTMLElement or as query string.\n     * @param {String} [popper.content=''] The content of the popper, it can be text, html, or node; if it is not text, set `contentType` to `html` or `node`.\n     * @param {String} [popper.contentType='text'] If `html`, the `content` will be parsed as HTML. If `node`, it will be appended as-is.\n     * @param {String} [popper.arrowTagName='div'] Same as `popper.tagName` but for the arrow element.\n     * @param {Array} [popper.arrowClassNames='popper__arrow'] Same as `popper.classNames` but for the arrow element.\n     * @param {String} [popper.arrowAttributes=['x-arrow']] Same as `popper.attributes` but for the arrow element.\n     * @param {Object} options\n     * @param {String} [options.placement=bottom]\n     *      Placement of the popper accepted values: `top(-start, -end), right(-start, -end), bottom(-start, -right),\n     *      left(-start, -end)`\n     *\n     * @param {HTMLElement|String} [options.arrowElement='[x-arrow]']\n     *      The DOM Node used as arrow for the popper, or a CSS selector used to get the DOM node. It must be child of\n     *      its parent Popper. Popper.js will apply to the given element the style required to align the arrow with its\n     *      reference element.\n     *      By default, it will look for a child node of the popper with the `x-arrow` attribute.\n     *\n     * @param {Boolean} [options.gpuAcceleration=true]\n     *      When this property is set to true, the popper position will be applied using CSS3 translate3d, allowing the\n     *      browser to use the GPU to accelerate the rendering.\n     *      If set to false, the popper will be placed using `top` and `left` properties, not using the GPU.\n     *\n     * @param {Number} [options.offset=0]\n     *      Amount of pixels the popper will be shifted (can be negative).\n     *\n     * @param {String|Element} [options.boundariesElement='viewport']\n     *      The element which will define the boundaries of the popper position, the popper will never be placed outside\n     *      of the defined boundaries (except if `keepTogether` is enabled)\n     *\n     * @param {Number} [options.boundariesPadding=5]\n     *      Additional padding for the boundaries\n     *\n     * @param {Array} [options.preventOverflowOrder=['left', 'right', 'top', 'bottom']]\n     *      Order used when Popper.js tries to avoid overflows from the boundaries, they will be checked in order,\n     *      this means that the last ones will never overflow\n     *\n     * @param {String|Array} [options.flipBehavior='flip']\n     *      The behavior used by the `flip` modifier to change the placement of the popper when the latter is trying to\n     *      overlap its reference element. Defining `flip` as value, the placement will be flipped on\n     *      its axis (`right - left`, `top - bottom`).\n     *      You can even pass an array of placements (eg: `['right', 'left', 'top']` ) to manually specify\n     *      how alter the placement when a flip is needed. (eg. in the above example, it would first flip from right to left,\n     *      then, if even in its new placement, the popper is overlapping its reference element, it will be moved to top)\n     *\n     * @param {Array} [options.modifiers=[ 'shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle']]\n     *      List of functions used to modify the data before they are applied to the popper, add your custom functions\n     *      to this array to edit the offsets and placement.\n     *      The function should reflect the @params and @returns of preventOverflow\n     *\n     * @param {Array} [options.modifiersIgnored=[]]\n     *      Put here any built-in modifier name you want to exclude from the modifiers list\n     *      The function should reflect the @params and @returns of preventOverflow\n     *\n     * @param {Boolean} [options.removeOnDestroy=false]\n     *      Set to true if you want to automatically remove the popper when you call the `destroy` method.\n     */\n    function Popper(reference, popper, options) {\n        this._reference = reference.jquery ? reference[0] : reference;\n        this.state = {};\n\n        // if the popper variable is a configuration object, parse it to generate an HTMLElement\n        // generate a default popper if is not defined\n        var isNotDefined = typeof popper === 'undefined' || popper === null;\n        var isConfig = popper && Object.prototype.toString.call(popper) === '[object Object]';\n        if (isNotDefined || isConfig) {\n            this._popper = this.parse(isConfig ? popper : {});\n        }\n        // otherwise, use the given HTMLElement as popper\n        else {\n                this._popper = popper.jquery ? popper[0] : popper;\n            }\n\n        // with {} we create a new object with the options inside it\n        this._options = Object.assign({}, DEFAULTS, options);\n\n        // refactoring modifiers' list\n        this._options.modifiers = this._options.modifiers.map(function (modifier) {\n            // remove ignored modifiers\n            if (this._options.modifiersIgnored.indexOf(modifier) !== -1) return;\n\n            // set the x-placement attribute before everything else because it could be used to add margins to the popper\n            // margins needs to be calculated to get the correct popper offsets\n            if (modifier === 'applyStyle') {\n                this._popper.setAttribute('x-placement', this._options.placement);\n            }\n\n            // return predefined modifier identified by string or keep the custom one\n            return this.modifiers[modifier] || modifier;\n        }.bind(this));\n\n        // make sure to apply the popper position before any computation\n        this.state.position = this._getPosition(this._popper, this._reference);\n        setStyle(this._popper, { position: this.state.position, top: 0 });\n\n        // fire the first update to position the popper in the right place\n        this.update();\n\n        // setup event listeners, they will take care of update the position in specific situations\n        this._setupEventListeners();\n        return this;\n    }\n\n    //\n    // Methods\n    //\n    /**\n     * Destroy the popper\n     * @method\n     * @memberof Popper\n     */\n    Popper.prototype.destroy = function () {\n        this._popper.removeAttribute('x-placement');\n        this._popper.style.left = '';\n        this._popper.style.position = '';\n        this._popper.style.top = '';\n        this._popper.style[getSupportedPropertyName('transform')] = '';\n        this._removeEventListeners();\n\n        // remove the popper if user explicity asked for the deletion on destroy\n        if (this._options.removeOnDestroy) {\n            this._popper.remove();\n        }\n        return this;\n    };\n\n    /**\n     * Updates the position of the popper, computing the new offsets and applying the new style\n     * @method\n     * @memberof Popper\n     */\n    Popper.prototype.update = function () {\n        var data = { instance: this, styles: {} };\n\n        // store placement inside the data object, modifiers will be able to edit `placement` if needed\n        // and refer to _originalPlacement to know the original value\n        data.placement = this._options.placement;\n        data._originalPlacement = this._options.placement;\n\n        // compute the popper and reference offsets and put them inside data.offsets\n        data.offsets = this._getOffsets(this._popper, this._reference, data.placement);\n\n        // get boundaries\n        data.boundaries = this._getBoundaries(data, this._options.boundariesPadding, this._options.boundariesElement);\n\n        data = this.runModifiers(data, this._options.modifiers);\n\n        if (typeof this.state.updateCallback === 'function') {\n            this.state.updateCallback(data);\n        }\n    };\n\n    /**\n     * If a function is passed, it will be executed after the initialization of popper with as first argument the Popper instance.\n     * @method\n     * @memberof Popper\n     * @param {Function} callback\n     */\n    Popper.prototype.onCreate = function (callback) {\n        // the createCallbacks return as first argument the popper instance\n        callback(this);\n        return this;\n    };\n\n    /**\n     * If a function is passed, it will be executed after each update of popper with as first argument the set of coordinates and informations\n     * used to style popper and its arrow.\n     * NOTE: it doesn't get fired on the first call of the `Popper.update()` method inside the `Popper` constructor!\n     * @method\n     * @memberof Popper\n     * @param {Function} callback\n     */\n    Popper.prototype.onUpdate = function (callback) {\n        this.state.updateCallback = callback;\n        return this;\n    };\n\n    /**\n     * Helper used to generate poppers from a configuration file\n     * @method\n     * @memberof Popper\n     * @param config {Object} configuration\n     * @returns {HTMLElement} popper\n     */\n    Popper.prototype.parse = function (config) {\n        var defaultConfig = {\n            tagName: 'div',\n            classNames: ['popper'],\n            attributes: [],\n            parent: root.document.body,\n            content: '',\n            contentType: 'text',\n            arrowTagName: 'div',\n            arrowClassNames: ['popper__arrow'],\n            arrowAttributes: ['x-arrow']\n        };\n        config = Object.assign({}, defaultConfig, config);\n\n        var d = root.document;\n\n        var popper = d.createElement(config.tagName);\n        addClassNames(popper, config.classNames);\n        addAttributes(popper, config.attributes);\n        if (config.contentType === 'node') {\n            popper.appendChild(config.content.jquery ? config.content[0] : config.content);\n        } else if (config.contentType === 'html') {\n            popper.innerHTML = config.content;\n        } else {\n            popper.textContent = config.content;\n        }\n\n        if (config.arrowTagName) {\n            var arrow = d.createElement(config.arrowTagName);\n            addClassNames(arrow, config.arrowClassNames);\n            addAttributes(arrow, config.arrowAttributes);\n            popper.appendChild(arrow);\n        }\n\n        var parent = config.parent.jquery ? config.parent[0] : config.parent;\n\n        // if the given parent is a string, use it to match an element\n        // if more than one element is matched, the first one will be used as parent\n        // if no elements are matched, the script will throw an error\n        if (typeof parent === 'string') {\n            parent = d.querySelectorAll(config.parent);\n            if (parent.length > 1) {\n                console.warn('WARNING: the given `parent` query(' + config.parent + ') matched more than one element, the first one will be used');\n            }\n            if (parent.length === 0) {\n                throw 'ERROR: the given `parent` doesn\\'t exists!';\n            }\n            parent = parent[0];\n        }\n        // if the given parent is a DOM nodes list or an array of nodes with more than one element,\n        // the first one will be used as parent\n        if (parent.length > 1 && parent instanceof Element === false) {\n            console.warn('WARNING: you have passed as parent a list of elements, the first one will be used');\n            parent = parent[0];\n        }\n\n        // append the generated popper to its parent\n        parent.appendChild(popper);\n\n        return popper;\n\n        /**\n         * Adds class names to the given element\n         * @function\n         * @ignore\n         * @param {HTMLElement} target\n         * @param {Array} classes\n         */\n        function addClassNames(element, classNames) {\n            classNames.forEach(function (className) {\n                element.classList.add(className);\n            });\n        }\n\n        /**\n         * Adds attributes to the given element\n         * @function\n         * @ignore\n         * @param {HTMLElement} target\n         * @param {Array} attributes\n         * @example\n         * addAttributes(element, [ 'data-info:foobar' ]);\n         */\n        function addAttributes(element, attributes) {\n            attributes.forEach(function (attribute) {\n                element.setAttribute(attribute.split(':')[0], attribute.split(':')[1] || '');\n            });\n        }\n    };\n\n    /**\n     * Helper used to get the position which will be applied to the popper\n     * @method\n     * @memberof Popper\n     * @param config {HTMLElement} popper element\n     * @param reference {HTMLElement} reference element\n     * @returns {String} position\n     */\n    Popper.prototype._getPosition = function (popper, reference) {\n        var container = getOffsetParent(reference);\n\n        if (this._options.forceAbsolute) {\n            return 'absolute';\n        }\n\n        // Decide if the popper will be fixed\n        // If the reference element is inside a fixed context, the popper will be fixed as well to allow them to scroll together\n        var isParentFixed = isFixed(reference, container);\n        return isParentFixed ? 'fixed' : 'absolute';\n    };\n\n    /**\n     * Get offsets to the popper\n     * @method\n     * @memberof Popper\n     * @access private\n     * @param {Element} popper - the popper element\n     * @param {Element} reference - the reference element (the popper will be relative to this)\n     * @returns {Object} An object containing the offsets which will be applied to the popper\n     */\n    Popper.prototype._getOffsets = function (popper, reference, placement) {\n        placement = placement.split('-')[0];\n        var popperOffsets = {};\n\n        popperOffsets.position = this.state.position;\n        var isParentFixed = popperOffsets.position === 'fixed';\n\n        //\n        // Get reference element position\n        //\n        var referenceOffsets = getOffsetRectRelativeToCustomParent(reference, getOffsetParent(popper), isParentFixed);\n\n        //\n        // Get popper sizes\n        //\n        var popperRect = getOuterSizes(popper);\n\n        //\n        // Compute offsets of popper\n        //\n\n        // depending by the popper placement we have to compute its offsets slightly differently\n        if (['right', 'left'].indexOf(placement) !== -1) {\n            popperOffsets.top = referenceOffsets.top + referenceOffsets.height / 2 - popperRect.height / 2;\n            if (placement === 'left') {\n                popperOffsets.left = referenceOffsets.left - popperRect.width;\n            } else {\n                popperOffsets.left = referenceOffsets.right;\n            }\n        } else {\n            popperOffsets.left = referenceOffsets.left + referenceOffsets.width / 2 - popperRect.width / 2;\n            if (placement === 'top') {\n                popperOffsets.top = referenceOffsets.top - popperRect.height;\n            } else {\n                popperOffsets.top = referenceOffsets.bottom;\n            }\n        }\n\n        // Add width and height to our offsets object\n        popperOffsets.width = popperRect.width;\n        popperOffsets.height = popperRect.height;\n\n        return {\n            popper: popperOffsets,\n            reference: referenceOffsets\n        };\n    };\n\n    /**\n     * Setup needed event listeners used to update the popper position\n     * @method\n     * @memberof Popper\n     * @access private\n     */\n    Popper.prototype._setupEventListeners = function () {\n        // NOTE: 1 DOM access here\n        this.state.updateBound = this.update.bind(this);\n        root.addEventListener('resize', this.state.updateBound);\n        // if the boundariesElement is window we don't need to listen for the scroll event\n        if (this._options.boundariesElement !== 'window') {\n            var target = getScrollParent(this._reference);\n            // here it could be both `body` or `documentElement` thanks to Firefox, we then check both\n            if (target === root.document.body || target === root.document.documentElement) {\n                target = root;\n            }\n            target.addEventListener('scroll', this.state.updateBound);\n            this.state.scrollTarget = target;\n        }\n    };\n\n    /**\n     * Remove event listeners used to update the popper position\n     * @method\n     * @memberof Popper\n     * @access private\n     */\n    Popper.prototype._removeEventListeners = function () {\n        // NOTE: 1 DOM access here\n        root.removeEventListener('resize', this.state.updateBound);\n        if (this._options.boundariesElement !== 'window' && this.state.scrollTarget) {\n            this.state.scrollTarget.removeEventListener('scroll', this.state.updateBound);\n            this.state.scrollTarget = null;\n        }\n        this.state.updateBound = null;\n    };\n\n    /**\n     * Computed the boundaries limits and return them\n     * @method\n     * @memberof Popper\n     * @access private\n     * @param {Object} data - Object containing the property \"offsets\" generated by `_getOffsets`\n     * @param {Number} padding - Boundaries padding\n     * @param {Element} boundariesElement - Element used to define the boundaries\n     * @returns {Object} Coordinates of the boundaries\n     */\n    Popper.prototype._getBoundaries = function (data, padding, boundariesElement) {\n        // NOTE: 1 DOM access here\n        var boundaries = {};\n        var width, height;\n        if (boundariesElement === 'window') {\n            var body = root.document.body,\n                html = root.document.documentElement;\n\n            height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n            width = Math.max(body.scrollWidth, body.offsetWidth, html.clientWidth, html.scrollWidth, html.offsetWidth);\n\n            boundaries = {\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0\n            };\n        } else if (boundariesElement === 'viewport') {\n            var offsetParent = getOffsetParent(this._popper);\n            var scrollParent = getScrollParent(this._popper);\n            var offsetParentRect = getOffsetRect(offsetParent);\n\n            // Thanks the fucking native API, `document.body.scrollTop` & `document.documentElement.scrollTop`\n            var getScrollTopValue = function getScrollTopValue(element) {\n                return element == document.body ? Math.max(document.documentElement.scrollTop, document.body.scrollTop) : element.scrollTop;\n            };\n            var getScrollLeftValue = function getScrollLeftValue(element) {\n                return element == document.body ? Math.max(document.documentElement.scrollLeft, document.body.scrollLeft) : element.scrollLeft;\n            };\n\n            // if the popper is fixed we don't have to substract scrolling from the boundaries\n            var scrollTop = data.offsets.popper.position === 'fixed' ? 0 : getScrollTopValue(scrollParent);\n            var scrollLeft = data.offsets.popper.position === 'fixed' ? 0 : getScrollLeftValue(scrollParent);\n\n            boundaries = {\n                top: 0 - (offsetParentRect.top - scrollTop),\n                right: root.document.documentElement.clientWidth - (offsetParentRect.left - scrollLeft),\n                bottom: root.document.documentElement.clientHeight - (offsetParentRect.top - scrollTop),\n                left: 0 - (offsetParentRect.left - scrollLeft)\n            };\n        } else {\n            if (getOffsetParent(this._popper) === boundariesElement) {\n                boundaries = {\n                    top: 0,\n                    left: 0,\n                    right: boundariesElement.clientWidth,\n                    bottom: boundariesElement.clientHeight\n                };\n            } else {\n                boundaries = getOffsetRect(boundariesElement);\n            }\n        }\n        boundaries.left += padding;\n        boundaries.right -= padding;\n        boundaries.top = boundaries.top + padding;\n        boundaries.bottom = boundaries.bottom - padding;\n        return boundaries;\n    };\n\n    /**\n     * Loop trough the list of modifiers and run them in order, each of them will then edit the data object\n     * @method\n     * @memberof Popper\n     * @access public\n     * @param {Object} data\n     * @param {Array} modifiers\n     * @param {Function} ends\n     */\n    Popper.prototype.runModifiers = function (data, modifiers, ends) {\n        var modifiersToRun = modifiers.slice();\n        if (ends !== undefined) {\n            modifiersToRun = this._options.modifiers.slice(0, getArrayKeyIndex(this._options.modifiers, ends));\n        }\n\n        modifiersToRun.forEach(function (modifier) {\n            if (isFunction(modifier)) {\n                data = modifier.call(this, data);\n            }\n        }.bind(this));\n\n        return data;\n    };\n\n    /**\n     * Helper used to know if the given modifier depends from another one.\n     * @method\n     * @memberof Popper\n     * @param {String} requesting - name of requesting modifier\n     * @param {String} requested - name of requested modifier\n     * @returns {Boolean}\n     */\n    Popper.prototype.isModifierRequired = function (requesting, requested) {\n        var index = getArrayKeyIndex(this._options.modifiers, requesting);\n        return !!this._options.modifiers.slice(0, index).filter(function (modifier) {\n            return modifier === requested;\n        }).length;\n    };\n\n    //\n    // Modifiers\n    //\n\n    /**\n     * Modifiers list\n     * @namespace Popper.modifiers\n     * @memberof Popper\n     * @type {Object}\n     */\n    Popper.prototype.modifiers = {};\n\n    /**\n     * Apply the computed styles to the popper element\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The same data object\n     */\n    Popper.prototype.modifiers.applyStyle = function (data) {\n        // apply the final offsets to the popper\n        // NOTE: 1 DOM access here\n        var styles = {\n            position: data.offsets.popper.position\n        };\n\n        // round top and left to avoid blurry text\n        var left = Math.round(data.offsets.popper.left);\n        var top = Math.round(data.offsets.popper.top);\n\n        // if gpuAcceleration is set to true and transform is supported, we use `translate3d` to apply the position to the popper\n        // we automatically use the supported prefixed version if needed\n        var prefixedProperty;\n        if (this._options.gpuAcceleration && (prefixedProperty = getSupportedPropertyName('transform'))) {\n            styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n            styles.top = 0;\n            styles.left = 0;\n        }\n        // othwerise, we use the standard `left` and `top` properties\n        else {\n                styles.left = left;\n                styles.top = top;\n            }\n\n        // any property present in `data.styles` will be applied to the popper,\n        // in this way we can make the 3rd party modifiers add custom styles to it\n        // Be aware, modifiers could override the properties defined in the previous\n        // lines of this modifier!\n        Object.assign(styles, data.styles);\n\n        setStyle(this._popper, styles);\n\n        // set an attribute which will be useful to style the tooltip (use it to properly position its arrow)\n        // NOTE: 1 DOM access here\n        this._popper.setAttribute('x-placement', data.placement);\n\n        // if the arrow modifier is required and the arrow style has been computed, apply the arrow style\n        if (this.isModifierRequired(this.modifiers.applyStyle, this.modifiers.arrow) && data.offsets.arrow) {\n            setStyle(data.arrowElement, data.offsets.arrow);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to shift the popper on the start or end of its reference element side\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.shift = function (data) {\n        var placement = data.placement;\n        var basePlacement = placement.split('-')[0];\n        var shiftVariation = placement.split('-')[1];\n\n        // if shift shiftVariation is specified, run the modifier\n        if (shiftVariation) {\n            var reference = data.offsets.reference;\n            var popper = getPopperClientRect(data.offsets.popper);\n\n            var shiftOffsets = {\n                y: {\n                    start: { top: reference.top },\n                    end: { top: reference.top + reference.height - popper.height }\n                },\n                x: {\n                    start: { left: reference.left },\n                    end: { left: reference.left + reference.width - popper.width }\n                }\n            };\n\n            var axis = ['bottom', 'top'].indexOf(basePlacement) !== -1 ? 'x' : 'y';\n\n            data.offsets.popper = Object.assign(popper, shiftOffsets[axis][shiftVariation]);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to make sure the popper does not overflows from it's boundaries\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.preventOverflow = function (data) {\n        var order = this._options.preventOverflowOrder;\n        var popper = getPopperClientRect(data.offsets.popper);\n\n        var check = {\n            left: function left() {\n                var left = popper.left;\n                if (popper.left < data.boundaries.left) {\n                    left = Math.max(popper.left, data.boundaries.left);\n                }\n                return { left: left };\n            },\n            right: function right() {\n                var left = popper.left;\n                if (popper.right > data.boundaries.right) {\n                    left = Math.min(popper.left, data.boundaries.right - popper.width);\n                }\n                return { left: left };\n            },\n            top: function top() {\n                var top = popper.top;\n                if (popper.top < data.boundaries.top) {\n                    top = Math.max(popper.top, data.boundaries.top);\n                }\n                return { top: top };\n            },\n            bottom: function bottom() {\n                var top = popper.top;\n                if (popper.bottom > data.boundaries.bottom) {\n                    top = Math.min(popper.top, data.boundaries.bottom - popper.height);\n                }\n                return { top: top };\n            }\n        };\n\n        order.forEach(function (direction) {\n            data.offsets.popper = Object.assign(popper, check[direction]());\n        });\n\n        return data;\n    };\n\n    /**\n     * Modifier used to make sure the popper is always near its reference\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.keepTogether = function (data) {\n        var popper = getPopperClientRect(data.offsets.popper);\n        var reference = data.offsets.reference;\n        var f = Math.floor;\n\n        if (popper.right < f(reference.left)) {\n            data.offsets.popper.left = f(reference.left) - popper.width;\n        }\n        if (popper.left > f(reference.right)) {\n            data.offsets.popper.left = f(reference.right);\n        }\n        if (popper.bottom < f(reference.top)) {\n            data.offsets.popper.top = f(reference.top) - popper.height;\n        }\n        if (popper.top > f(reference.bottom)) {\n            data.offsets.popper.top = f(reference.bottom);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to flip the placement of the popper when the latter is starting overlapping its reference element.\n     * Requires the `preventOverflow` modifier before it in order to work.\n     * **NOTE:** This modifier will run all its previous modifiers everytime it tries to flip the popper!\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.flip = function (data) {\n        // check if preventOverflow is in the list of modifiers before the flip modifier.\n        // otherwise flip would not work as expected.\n        if (!this.isModifierRequired(this.modifiers.flip, this.modifiers.preventOverflow)) {\n            console.warn('WARNING: preventOverflow modifier is required by flip modifier in order to work, be sure to include it before flip!');\n            return data;\n        }\n\n        if (data.flipped && data.placement === data._originalPlacement) {\n            // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n            return data;\n        }\n\n        var placement = data.placement.split('-')[0];\n        var placementOpposite = getOppositePlacement(placement);\n        var variation = data.placement.split('-')[1] || '';\n\n        var flipOrder = [];\n        if (this._options.flipBehavior === 'flip') {\n            flipOrder = [placement, placementOpposite];\n        } else {\n            flipOrder = this._options.flipBehavior;\n        }\n\n        flipOrder.forEach(function (step, index) {\n            if (placement !== step || flipOrder.length === index + 1) {\n                return;\n            }\n\n            placement = data.placement.split('-')[0];\n            placementOpposite = getOppositePlacement(placement);\n\n            var popperOffsets = getPopperClientRect(data.offsets.popper);\n\n            // this boolean is used to distinguish right and bottom from top and left\n            // they need different computations to get flipped\n            var a = ['right', 'bottom'].indexOf(placement) !== -1;\n\n            // using Math.floor because the reference offsets may contain decimals we are not going to consider here\n            if (a && Math.floor(data.offsets.reference[placement]) > Math.floor(popperOffsets[placementOpposite]) || !a && Math.floor(data.offsets.reference[placement]) < Math.floor(popperOffsets[placementOpposite])) {\n                // we'll use this boolean to detect any flip loop\n                data.flipped = true;\n                data.placement = flipOrder[index + 1];\n                if (variation) {\n                    data.placement += '-' + variation;\n                }\n                data.offsets.popper = this._getOffsets(this._popper, this._reference, data.placement).popper;\n\n                data = this.runModifiers(data, this._options.modifiers, this._flip);\n            }\n        }.bind(this));\n        return data;\n    };\n\n    /**\n     * Modifier used to add an offset to the popper, useful if you more granularity positioning your popper.\n     * The offsets will shift the popper on the side of its reference element.\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.offset = function (data) {\n        var offset = this._options.offset;\n        var popper = data.offsets.popper;\n\n        if (data.placement.indexOf('left') !== -1) {\n            popper.top -= offset;\n        } else if (data.placement.indexOf('right') !== -1) {\n            popper.top += offset;\n        } else if (data.placement.indexOf('top') !== -1) {\n            popper.left -= offset;\n        } else if (data.placement.indexOf('bottom') !== -1) {\n            popper.left += offset;\n        }\n        return data;\n    };\n\n    /**\n     * Modifier used to move the arrows on the edge of the popper to make sure them are always between the popper and the reference element\n     * It will use the CSS outer size of the arrow element to know how many pixels of conjuction are needed\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.arrow = function (data) {\n        var arrow = this._options.arrowElement;\n        var arrowOffset = this._options.arrowOffset;\n\n        // if the arrowElement is a string, suppose it's a CSS selector\n        if (typeof arrow === 'string') {\n            arrow = this._popper.querySelector(arrow);\n        }\n\n        // if arrow element is not found, don't run the modifier\n        if (!arrow) {\n            return data;\n        }\n\n        // the arrow element must be child of its popper\n        if (!this._popper.contains(arrow)) {\n            console.warn('WARNING: `arrowElement` must be child of its popper element!');\n            return data;\n        }\n\n        // arrow depends on keepTogether in order to work\n        if (!this.isModifierRequired(this.modifiers.arrow, this.modifiers.keepTogether)) {\n            console.warn('WARNING: keepTogether modifier is required by arrow modifier in order to work, be sure to include it before arrow!');\n            return data;\n        }\n\n        var arrowStyle = {};\n        var placement = data.placement.split('-')[0];\n        var popper = getPopperClientRect(data.offsets.popper);\n        var reference = data.offsets.reference;\n        var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n        var len = isVertical ? 'height' : 'width';\n        var side = isVertical ? 'top' : 'left';\n        var translate = isVertical ? 'translateY' : 'translateX';\n        var altSide = isVertical ? 'left' : 'top';\n        var opSide = isVertical ? 'bottom' : 'right';\n        var arrowSize = getOuterSizes(arrow)[len];\n\n        //\n        // extends keepTogether behavior making sure the popper and its reference have enough pixels in conjuction\n        //\n\n        // top/left side\n        if (reference[opSide] - arrowSize < popper[side]) {\n            data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowSize);\n        }\n        // bottom/right side\n        if (reference[side] + arrowSize > popper[opSide]) {\n            data.offsets.popper[side] += reference[side] + arrowSize - popper[opSide];\n        }\n\n        // compute center of the popper\n        var center = reference[side] + (arrowOffset || reference[len] / 2 - arrowSize / 2);\n\n        var sideValue = center - popper[side];\n\n        // prevent arrow from being placed not contiguously to its popper\n        sideValue = Math.max(Math.min(popper[len] - arrowSize - 8, sideValue), 8);\n        arrowStyle[side] = sideValue;\n        arrowStyle[altSide] = ''; // make sure to remove any old style from the arrow\n\n        data.offsets.arrow = arrowStyle;\n        data.arrowElement = arrow;\n\n        return data;\n    };\n\n    //\n    // Helpers\n    //\n\n    /**\n     * Get the outer sizes of the given element (offset size + margins)\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Object} object containing width and height properties\n     */\n    function getOuterSizes(element) {\n        // NOTE: 1 DOM access here\n        var _display = element.style.display,\n            _visibility = element.style.visibility;\n        element.style.display = 'block';element.style.visibility = 'hidden';\n        var calcWidthToForceRepaint = element.offsetWidth;\n\n        // original method\n        var styles = root.getComputedStyle(element);\n        var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n        var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n        var result = { width: element.offsetWidth + y, height: element.offsetHeight + x };\n\n        // reset element styles\n        element.style.display = _display;element.style.visibility = _visibility;\n        return result;\n    }\n\n    /**\n     * Get the opposite placement of the given one/\n     * @function\n     * @ignore\n     * @argument {String} placement\n     * @returns {String} flipped placement\n     */\n    function getOppositePlacement(placement) {\n        var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n        return placement.replace(/left|right|bottom|top/g, function (matched) {\n            return hash[matched];\n        });\n    }\n\n    /**\n     * Given the popper offsets, generate an output similar to getBoundingClientRect\n     * @function\n     * @ignore\n     * @argument {Object} popperOffsets\n     * @returns {Object} ClientRect like output\n     */\n    function getPopperClientRect(popperOffsets) {\n        var offsets = Object.assign({}, popperOffsets);\n        offsets.right = offsets.left + offsets.width;\n        offsets.bottom = offsets.top + offsets.height;\n        return offsets;\n    }\n\n    /**\n     * Given an array and the key to find, returns its index\n     * @function\n     * @ignore\n     * @argument {Array} arr\n     * @argument keyToFind\n     * @returns index or null\n     */\n    function getArrayKeyIndex(arr, keyToFind) {\n        var i = 0,\n            key;\n        for (key in arr) {\n            if (arr[key] === keyToFind) {\n                return i;\n            }\n            i++;\n        }\n        return null;\n    }\n\n    /**\n     * Get CSS computed property of the given element\n     * @function\n     * @ignore\n     * @argument {Eement} element\n     * @argument {String} property\n     */\n    function getStyleComputedProperty(element, property) {\n        // NOTE: 1 DOM access here\n        var css = root.getComputedStyle(element, null);\n        return css[property];\n    }\n\n    /**\n     * Returns the offset parent of the given element\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Element} offset parent\n     */\n    function getOffsetParent(element) {\n        // NOTE: 1 DOM access here\n        var offsetParent = element.offsetParent;\n        return offsetParent === root.document.body || !offsetParent ? root.document.documentElement : offsetParent;\n    }\n\n    /**\n     * Returns the scrolling parent of the given element\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Element} offset parent\n     */\n    function getScrollParent(element) {\n        var parent = element.parentNode;\n\n        if (!parent) {\n            return element;\n        }\n\n        if (parent === root.document) {\n            // Firefox puts the scrollTOp value on `documentElement` instead of `body`, we then check which of them is\n            // greater than 0 and return the proper element\n            if (root.document.body.scrollTop || root.document.body.scrollLeft) {\n                return root.document.body;\n            } else {\n                return root.document.documentElement;\n            }\n        }\n\n        // Firefox want us to check `-x` and `-y` variations as well\n        if (['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-x')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-y')) !== -1) {\n            // If the detected scrollParent is body, we perform an additional check on its parentNode\n            // in this way we'll get body if the browser is Chrome-ish, or documentElement otherwise\n            // fixes issue #65\n            return parent;\n        }\n        return getScrollParent(element.parentNode);\n    }\n\n    /**\n     * Check if the given element is fixed or is inside a fixed parent\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @argument {Element} customContainer\n     * @returns {Boolean} answer to \"isFixed?\"\n     */\n    function isFixed(element) {\n        if (element === root.document.body) {\n            return false;\n        }\n        if (getStyleComputedProperty(element, 'position') === 'fixed') {\n            return true;\n        }\n        return element.parentNode ? isFixed(element.parentNode) : element;\n    }\n\n    /**\n     * Set the style to the given popper\n     * @function\n     * @ignore\n     * @argument {Element} element - Element to apply the style to\n     * @argument {Object} styles - Object with a list of properties and values which will be applied to the element\n     */\n    function setStyle(element, styles) {\n        function is_numeric(n) {\n            return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n        }\n        Object.keys(styles).forEach(function (prop) {\n            var unit = '';\n            // add unit if the value is numeric and is one of the following\n            if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && is_numeric(styles[prop])) {\n                unit = 'px';\n            }\n            element.style[prop] = styles[prop] + unit;\n        });\n    }\n\n    /**\n     * Check if the given variable is a function\n     * @function\n     * @ignore\n     * @argument {*} functionToCheck - variable to check\n     * @returns {Boolean} answer to: is a function?\n     */\n    function isFunction(functionToCheck) {\n        var getType = {};\n        return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n    }\n\n    /**\n     * Get the position of the given element, relative to its offset parent\n     * @function\n     * @ignore\n     * @param {Element} element\n     * @return {Object} position - Coordinates of the element and its `scrollTop`\n     */\n    function getOffsetRect(element) {\n        var elementRect = {\n            width: element.offsetWidth,\n            height: element.offsetHeight,\n            left: element.offsetLeft,\n            top: element.offsetTop\n        };\n\n        elementRect.right = elementRect.left + elementRect.width;\n        elementRect.bottom = elementRect.top + elementRect.height;\n\n        // position\n        return elementRect;\n    }\n\n    /**\n     * Get bounding client rect of given element\n     * @function\n     * @ignore\n     * @param {HTMLElement} element\n     * @return {Object} client rect\n     */\n    function getBoundingClientRect(element) {\n        var rect = element.getBoundingClientRect();\n\n        // whether the IE version is lower than 11\n        var isIE = navigator.userAgent.indexOf(\"MSIE\") != -1;\n\n        // fix ie document bounding top always 0 bug\n        var rectTop = isIE && element.tagName === 'HTML' ? -element.scrollTop : rect.top;\n\n        return {\n            left: rect.left,\n            top: rectTop,\n            right: rect.right,\n            bottom: rect.bottom,\n            width: rect.right - rect.left,\n            height: rect.bottom - rectTop\n        };\n    }\n\n    /**\n     * Given an element and one of its parents, return the offset\n     * @function\n     * @ignore\n     * @param {HTMLElement} element\n     * @param {HTMLElement} parent\n     * @return {Object} rect\n     */\n    function getOffsetRectRelativeToCustomParent(element, parent, fixed) {\n        var elementRect = getBoundingClientRect(element);\n        var parentRect = getBoundingClientRect(parent);\n\n        if (fixed) {\n            var scrollParent = getScrollParent(parent);\n            parentRect.top += scrollParent.scrollTop;\n            parentRect.bottom += scrollParent.scrollTop;\n            parentRect.left += scrollParent.scrollLeft;\n            parentRect.right += scrollParent.scrollLeft;\n        }\n\n        var rect = {\n            top: elementRect.top - parentRect.top,\n            left: elementRect.left - parentRect.left,\n            bottom: elementRect.top - parentRect.top + elementRect.height,\n            right: elementRect.left - parentRect.left + elementRect.width,\n            width: elementRect.width,\n            height: elementRect.height\n        };\n        return rect;\n    }\n\n    /**\n     * Get the prefixed supported property name\n     * @function\n     * @ignore\n     * @argument {String} property (camelCase)\n     * @returns {String} prefixed property (camelCase)\n     */\n    function getSupportedPropertyName(property) {\n        var prefixes = ['', 'ms', 'webkit', 'moz', 'o'];\n\n        for (var i = 0; i < prefixes.length; i++) {\n            var toCheck = prefixes[i] ? prefixes[i] + property.charAt(0).toUpperCase() + property.slice(1) : property;\n            if (typeof root.document.body.style[toCheck] !== 'undefined') {\n                return toCheck;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * The Object.assign() method is used to copy the values of all enumerable own properties from one or more source\n     * objects to a target object. It will return the target object.\n     * This polyfill doesn't support symbol properties, since ES5 doesn't have symbols anyway\n     * Source: https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n     * @function\n     * @ignore\n     */\n    if (!Object.assign) {\n        Object.defineProperty(Object, 'assign', {\n            enumerable: false,\n            configurable: true,\n            writable: true,\n            value: function value(target) {\n                if (target === undefined || target === null) {\n                    throw new TypeError('Cannot convert first argument to object');\n                }\n\n                var to = Object(target);\n                for (var i = 1; i < arguments.length; i++) {\n                    var nextSource = arguments[i];\n                    if (nextSource === undefined || nextSource === null) {\n                        continue;\n                    }\n                    nextSource = Object(nextSource);\n\n                    var keysArray = Object.keys(nextSource);\n                    for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {\n                        var nextKey = keysArray[nextIndex];\n                        var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n                        if (desc !== undefined && desc.enumerable) {\n                            to[nextKey] = nextSource[nextKey];\n                        }\n                    }\n                }\n                return to;\n            }\n        });\n    }\n\n    return Popper;\n});"], "names": [], "sourceRoot": ""}