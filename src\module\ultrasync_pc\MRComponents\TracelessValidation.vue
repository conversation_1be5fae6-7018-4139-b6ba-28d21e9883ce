<template>
    <div>
        <div id="nc" :key="key"></div>
    </div>

</template>

<script type="text/javascript"></script>
<script>
import base from '../lib/base'
import service from '../service/service'
export default {
    mixins: [base],
    name:'TracelessValidation',
    data(){
        return {
            isTracelessValiding:false,
            key:Date.now(),
        }
    },
    props:{

    },
    created(){
        this.initTracelessValid();
    },
    methods:{
        initTracelessValid(){
            let _this = this
            // 实例化nvc 对无痕验证进行初始化操作
            AWSC.use("nvc", function(state, module) {
                let option = {
                    appkey: "FFFF0N0000000000AF8F",
                    scene: "nvc_message_h5",
                    // 二次验证获取人机信息串，跟随业务请求一起上传至业务服务器，由业务服务器进行验签。
                    success: function(data) {
                        console.log('触发了二次验证！')
                        _this.$emit("reStartVerifyByTraceless");
                        _this.sendLoginRequest(data)
                    },
                    // 前端二次验证失败时触发该回调参数
                    fail: function (failCode) {
                        _this.isTracelessValiding=false;
                        _this.key=Date.now();
                        _this.initTracelessValid();
                        window.nvc.reset()
                        _this.$message.error(_this.$t('traceless_failed'))
                        console.log('failCode',failCode)
                        _this.$emit('errorVerifyByTraceless');
                    },
                    // 前端二次验证加载异常时触发该回调参数。
                    error: function (errorCode) {
                        _this.isTracelessValiding=false;
                        _this.key=Date.now();
                        _this.initTracelessValid();
                        window.nvc.reset()
                        _this.$message.error(_this.$t('traceless_failed'))
                        console.log('errorCode',errorCode)
                        _this.$emit('errorVerifyByTraceless');
                    }
                }
                window.nvc = module.init(option)
            });
        },
        loginByTracelessValid(){
            if(this.isTracelessValiding){
                return
            }
            this.isTracelessValiding=true;
            window.nvc.getNVCValAsync((nvcVal) => {
                this.sendLoginRequest(nvcVal)
            })
        },
        //发送网络请求
        sendLoginRequest(nvcVal){
            service.loginByTraceless({data:nvcVal}).then(res => {
                console.log('loginByTraceless result',res)
                // this.$message.error(this.$t('register_success'))
                if(res.data.error_code === 0){
                    this.verificationData(res.data.data)
                }else{
                    this.$emit('errorVerifyByTraceless');
                    this.isTracelessValiding=false;
                    this.$message.error(this.$t('traceless_failed'))
                }
            }).catch(error =>{
                this.$emit('errorVerifyByTraceless');
                this.isTracelessValiding=false;
                this.$message.error(error)
            })
        },
        verificationData(data){
            if (data.code === '200') {
                this.isTracelessValiding=false;
                let afsCode = data.afsCode
                this.$emit('successVerifyByTraceless', afsCode)
            } else if(data.code === '100'){
                window.nvc.reset()
                let afsCode = data.afsCode
                this.$emit('successVerifyByTraceless', afsCode)
            } else if (data.code === '800' || data.code === '900') {
                // 无痕验证失败，直接拦截
                window.nvc.reset()
                this.isTracelessValiding=false;
                this.$emit('errorVerifyByTraceless');
                this.$message.error(this.$t('traceless_failed'))
            } else if (data.code === '400') {
                window.nvc.reset()
                // 无痕验证失败，触发二次验证
                var ncoption = {
                    renderTo: "nc",
                    upLang:{
                        'cn': {
                            'LOADING': this.$t('bottom_loading_text'),
                            'SLIDE': this.$t('traceless_slide'),
                            'SUCCESS': this.$t('traceless_success'),
                            'ERROR': this.$t('traceless_error'),
                            'FAIL': this.$t('traceless_failed')
                        }
                    },
                    hideErrorCode:true,
                }
                // 唤醒二次验证（滑动验证码）
                window.nvc.getNC(ncoption);
                this.$emit('errorVerifyByTraceless');
                this.$emit('startSecondaryVerification');
            }
        },
    }
}
</script>

<style lang="scss">
#nc{
    position: relative;
    display: block;
    &.nc-container{
        margin-bottom:10px
    }
    .nc_wrapper{
        width: 100% !important;
        font-size: 13px !important;
        background: #dfdcdc;
    }
}
</style>

