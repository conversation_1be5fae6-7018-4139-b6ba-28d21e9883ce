import Vue from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import permissionManager from '@/common/permission'

const initState ={
    avatar:'',
    avatar_local:'',
    develop_mode:'',
    enable_fingerprint:false,
    enable_monitor_wall:false,
    id:'',
    is_into_tv_wall:0,
    language:'CN',
    msg:'',
    nickname:'用户昵称',
    request_tag:'',
    role:2,
    sex:1,
    title:'',
    type:4,
    uid:null,
    username:'',
    fromLogin:false,
    auto_join_group:null,
    probationary_expiry:"0000-00-00 00:00:00",
    is_enhance_password:false,
    is_password_privatized:0,
    organizationName:'',
    name: '',
    new_token: ''
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'user',cloneDeep(initState))
            }
        },
        updateUser(state, valObj) {
            // 记录更新前的关键信息
            const oldRole = state.role;
            const oldUserId = state.uid;

            // 更新state
            for (let key in valObj) {
                Vue.set(state,key,valObj[key]);
                // state[key] = valObj[key];
            }

            // 更新日志配置
            if (state.uid) {
                window.logReporter.setConfig({
                    uin:`${state.nickname}`
                })
            }else{
                window.logReporter.setConfig({
                    uin:''
                })
            }

            // 同步更新权限管理器的用户信息
            if (permissionManager && permissionManager.isInitialized()) {
                try {
                    // 检查是否有权限相关的字段更新
                    const hasPermissionRelatedChanges = valObj.role !== undefined ||
                                                       valObj.uid !== undefined ||
                                                       valObj.id !== undefined;

                    if (hasPermissionRelatedChanges) {
                        // 构建完整的用户信息对象
                        const userInfo = { ...state };
                        permissionManager.updateUserInfo(userInfo);

                        console.log('PC Store已同步更新权限管理器用户信息:', {
                            oldRole,
                            newRole: state.role,
                            oldUserId,
                            newUserId: state.uid,
                            updatedFields: Object.keys(valObj)
                        });
                    }
                } catch (error) {
                    console.error('PC Store同步权限管理器用户信息失败:', error);
                }
            }
        },
    },
    actions: {},
    getters: {}
}
